<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class Payment extends Model
{
    protected $fillable = [
        'student_id',
        'branch_id',
        'academy_id',
        'amount',
        'discount',
        'currency',
        'payment_method',
        'payment_date',
        'start_date',
        'end_date',
        'status',
        'reset_num',
        'class_time_from',
        'class_time_to',
        'renewal',
        'note',
        'reference_number',
        'description',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'discount' => 'decimal:2',
        'payment_date' => 'date',
        'start_date' => 'date',
        'end_date' => 'date',
        'renewal' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_amount',
        'net_amount',
        'formatted_net_amount',
        'status_text',
        'status_badge_class',
        'method_text',
        'formatted_payment_date',
        'formatted_start_date',
        'formatted_end_date',
    ];

    /**
     * Get the student that owns the payment.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the branch that owns the payment.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns the payment.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    // Computed Properties

    /**
     * Get formatted amount in AED.
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'AED ' . number_format($this->amount, 2);
    }

    /**
     * Get net amount after discount.
     */
    public function getNetAmountAttribute(): float
    {
        return $this->amount - ($this->discount ?? 0);
    }

    /**
     * Get formatted net amount in AED.
     */
    public function getFormattedNetAmountAttribute(): string
    {
        return 'AED ' . number_format($this->net_amount, 2);
    }

    /**
     * Get human-readable status text.
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            'completed' => 'Completed',
            'pending' => 'Pending',
            'failed' => 'Failed',
            'refunded' => 'Refunded',
            'cancelled' => 'Cancelled',
            default => 'Unknown'
        };
    }

    /**
     * Get status badge CSS class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'completed' => 'badge-success',
            'pending' => 'badge-warning',
            'failed' => 'badge-danger',
            'refunded' => 'badge-info',
            'cancelled' => 'badge-secondary',
            default => 'badge-secondary'
        };
    }

    /**
     * Get human-readable payment method text.
     */
    public function getMethodTextAttribute(): string
    {
        return match ($this->payment_method) {
            'cash' => 'Cash',
            'card' => 'Credit/Debit Card',
            'bank_transfer' => 'Bank Transfer',
            'online' => 'Online Payment',
            'cheque' => 'Cheque',
            default => 'Other'
        };
    }

    /**
     * Get formatted payment date.
     */
    public function getFormattedPaymentDateAttribute(): string
    {
        return $this->payment_date ? $this->payment_date->format('M d, Y') : '';
    }

    /**
     * Get formatted start date.
     */
    public function getFormattedStartDateAttribute(): string
    {
        return $this->start_date ? $this->start_date->format('M d, Y') : '';
    }

    /**
     * Get formatted end date.
     */
    public function getFormattedEndDateAttribute(): string
    {
        return $this->end_date ? $this->end_date->format('M d, Y') : '';
    }

    // Query Scopes

    /**
     * Scope a query to only include completed payments.
     */
    public function scopeCompleted(Builder $query): Builder
    {
        // Handle both new 'completed' status and legacy 'active' status
        return $query->whereIn('status', ['completed', 'active']);
    }

    /**
     * Scope a query to only include pending payments.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to filter by payment method.
     */
    public function scopeByMethod(Builder $query, string $method): Builder
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Scope a query to filter by branch.
     */
    public function scopeByBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope a query to filter by academy.
     */
    public function scopeByAcademy(Builder $query, int $academyId): Builder
    {
        return $query->where('academy_id', $academyId);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeByDateRange(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereBetween('payment_date', [$startDate, $endDate]);
    }

    // Utility Methods

    /**
     * Check if payment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Mark payment as completed.
     */
    public function markAsCompleted(): bool
    {
        $this->status = 'completed';
        $this->payment_date = now();
        return $this->save();
    }

    /**
     * Mark payment as failed.
     */
    public function markAsFailed(): bool
    {
        $this->status = 'failed';
        return $this->save();
    }

    /**
     * Generate reference number.
     */
    public static function generateReferenceNumber(): string
    {
        return 'PAY-' . date('Ymd') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
}
