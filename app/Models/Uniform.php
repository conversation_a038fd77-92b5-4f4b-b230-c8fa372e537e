<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Schema;

class Uniform extends Model
{
    protected $fillable = [
        'student_id',
        'branch_id',
        'academy_id',
        'order_date',
        'size',
        'amount',
        'currency',
        'branch_status',
        'office_status',
        'payment_method',
        'note',
        'item',
        'quantity',
        'delivery_date',
        'status',
    ];

    protected $casts = [
        'order_date' => 'date',
        'delivery_date' => 'date',
        'amount' => 'decimal:2',
        'quantity' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_amount',
        'total_amount',
        'formatted_total_amount',
        'status_text',
        'status_badge_class',
        'formatted_order_date',
        'formatted_delivery_date',
        'branch_status_text',
        'office_status_text',
    ];

    /**
     * Get the student that owns the uniform.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the branch that owns the uniform.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns the uniform.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    // Computed Properties

    /**
     * Get formatted amount in AED.
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'AED ' . number_format($this->amount, 2);
    }

    /**
     * Get total amount (quantity * amount).
     */
    public function getTotalAmountAttribute(): float
    {
        return ($this->quantity ?? 1) * $this->amount;
    }

    /**
     * Get formatted total amount in AED.
     */
    public function getFormattedTotalAmountAttribute(): string
    {
        return 'AED ' . number_format($this->total_amount, 2);
    }

    /**
     * Get human-readable status text.
     */
    public function getStatusTextAttribute(): string
    {
        // Handle both new 'status' column and legacy branch_status/office_status
        $status = $this->status ?? $this->getEffectiveStatus();

        return match ($status) {
            'ordered' => 'Ordered',
            'processing' => 'Processing',
            'ready' => 'Ready for Pickup',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
            'pending' => 'Pending',
            'received' => 'Received',
            default => 'Ordered'
        };
    }

    /**
     * Get status badge CSS class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        // Handle both new 'status' column and legacy branch_status/office_status
        $status = $this->status ?? $this->getEffectiveStatus();

        return match ($status) {
            'ordered' => 'badge-info',
            'processing' => 'badge-warning',
            'ready' => 'badge-primary',
            'delivered' => 'badge-success',
            'cancelled' => 'badge-danger',
            'pending' => 'badge-warning',
            'received' => 'badge-info',
            default => 'badge-info'
        };
    }

    /**
     * Get effective status from legacy columns if new status column doesn't exist.
     */
    private function getEffectiveStatus(): string
    {
        // If both branch and office have delivered, consider it delivered
        if ($this->branch_status === 'delivered' && $this->office_status === 'delivered') {
            return 'delivered';
        }

        // If either is received, consider it processing
        if ($this->branch_status === 'received' || $this->office_status === 'received') {
            return 'processing';
        }

        // Default to pending/ordered
        return 'ordered';
    }

    /**
     * Get formatted order date.
     */
    public function getFormattedOrderDateAttribute(): string
    {
        return $this->order_date ? $this->order_date->format('M d, Y') : '';
    }

    /**
     * Get formatted delivery date.
     */
    public function getFormattedDeliveryDateAttribute(): string
    {
        return $this->delivery_date ? $this->delivery_date->format('M d, Y') : '';
    }

    /**
     * Get branch status text.
     */
    public function getBranchStatusTextAttribute(): string
    {
        return match ($this->branch_status) {
            'pending' => 'Pending',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            default => 'Pending'
        };
    }

    /**
     * Get office status text.
     */
    public function getOfficeStatusTextAttribute(): string
    {
        return match ($this->office_status) {
            'pending' => 'Pending',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            default => 'Pending'
        };
    }

    // Query Scopes

    /**
     * Scope a query to only include ordered uniforms.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        // Check if status column exists, otherwise use branch_status
        if (Schema::hasColumn('uniforms', 'status')) {
            return $query->where('status', 'ordered');
        }
        return $query->where('branch_status', 'pending');
    }

    /**
     * Scope a query to only include delivered uniforms.
     */
    public function scopeDelivered(Builder $query): Builder
    {
        // Check if status column exists, otherwise use branch_status and office_status
        if (Schema::hasColumn('uniforms', 'status')) {
            return $query->where('status', 'delivered');
        }
        return $query->where('branch_status', 'delivered')
            ->where('office_status', 'delivered');
    }

    /**
     * Scope a query to filter by branch.
     */
    public function scopeByBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope a query to filter by academy.
     */
    public function scopeByAcademy(Builder $query, int $academyId): Builder
    {
        return $query->where('academy_id', $academyId);
    }

    /**
     * Scope a query to filter by branch status.
     */
    public function scopeByBranchStatus(Builder $query, string $status): Builder
    {
        return $query->where('branch_status', $status);
    }

    /**
     * Scope a query to filter by office status.
     */
    public function scopeByOfficeStatus(Builder $query, string $status): Builder
    {
        return $query->where('office_status', $status);
    }

    // Utility Methods

    /**
     * Check if uniform is delivered.
     */
    public function isDelivered(): bool
    {
        return $this->status === 'delivered';
    }

    /**
     * Mark uniform as delivered.
     */
    public function markAsDelivered(): bool
    {
        $this->status = 'delivered';
        $this->delivery_date = now();
        return $this->save();
    }

    /**
     * Get available uniform sizes.
     */
    public static function getAvailableSizes(): array
    {
        return [
            'XS' => 'Extra Small',
            'S' => 'Small',
            'M' => 'Medium',
            'L' => 'Large',
            'XL' => 'Extra Large',
            'XXL' => 'Double Extra Large',
        ];
    }
}
