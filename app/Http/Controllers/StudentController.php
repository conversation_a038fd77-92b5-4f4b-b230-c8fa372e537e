<?php

namespace App\Http\Controllers;

use App\Models\Student;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Payment;
use App\Models\Uniform;
use App\Models\Attendance;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class StudentController extends Controller
{
    /**
     * Display a listing of students with advanced search and filter capabilities.
     */
    public function index(Request $request): View|JsonResponse
    {
        $query = Student::with(['branch', 'academy', 'payments', 'uniforms', 'attendances'])
            ->withCount(['payments', 'uniforms', 'attendances']);

        // Advanced search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->search($search);
        }

        // Branch filter
        if ($request->filled('branch_id')) {
            $query->byBranch($request->get('branch_id'));
        }

        // Academy filter
        if ($request->filled('academy_id')) {
            $query->byAcademy($request->get('academy_id'));
        }

        // Status filter
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->active();
            } elseif ($status === 'inactive') {
                $query->inactive();
            } elseif ($status === 'suspended') {
                $query->suspended();
            }
        }

        // Age range filter
        if ($request->filled('min_age') && $request->filled('max_age')) {
            $query->byAgeRange($request->get('min_age'), $request->get('max_age'));
        }

        // Join date range filter
        if ($request->filled('join_start_date') && $request->filled('join_end_date')) {
            $query->byJoinDateRange($request->get('join_start_date'), $request->get('join_end_date'));
        }

        // Nationality filter
        if ($request->filled('nationality')) {
            $query->where('nationality', 'like', '%' . $request->get('nationality') . '%');
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $students = $query->paginate($perPage)->withQueryString();

        // Get branches and academies for filter dropdowns
        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        // Calculate statistics
        $stats = [
            'total_students' => Student::count(),
            'active_students' => Student::active()->count(),
            'inactive_students' => Student::inactive()->count(),
            'suspended_students' => Student::suspended()->count(),
            'total_payments' => Payment::completed()->sum('amount'),
            'pending_payments' => Payment::pending()->sum('amount'),
            'total_uniforms' => Uniform::count(),
            'delivered_uniforms' => Uniform::delivered()->count(),
        ];

        if ($request->ajax()) {
            return response()->json([
                'students' => $students,
                'stats' => $stats
            ]);
        }

        return view('students.index', compact('students', 'branches', 'academies', 'stats'));
    }

    /**
     * Show the form for creating a new student.
     */
    public function create(): View
    {
        Gate::authorize('create', Student::class);

        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        return view('students.create', compact('branches', 'academies'));
    }

    /**
     * Store a newly created student in storage.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', Student::class);

        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'academy_id' => 'required|exists:academies,id',
            'full_name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:students,email',
            'phone' => [
                'required',
                'string',
                'regex:/^\+971[0-9]{9}$/',
                'unique:students,phone'
            ],
            'nationality' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:500',
            'birth_date' => 'nullable|date|before:today',
            'join_date' => 'required|date',
            'status' => 'required|in:active,inactive,suspended',
            'notes' => 'nullable|string|max:1000',
        ], [
            'phone.regex' => 'Phone number must be in UAE format: +971XXXXXXXXX',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $student = Student::create($validator->validated());

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Student created successfully',
                    'student' => $student->load(['branch', 'academy'])
                ]);
            }

            return redirect()->route('students.index')
                ->with('success', 'Student created successfully');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create student: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to create student: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified student with detailed information.
     */
    public function show(Student $student): View|JsonResponse
    {
        Gate::authorize('view', $student);

        $student->load([
            'branch',
            'academy',
            'payments.academy',
            'uniforms',
            'attendances.program'
        ]);

        // Get student statistics
        $stats = $student->getStatistics();

        // Get payment history
        $paymentHistory = $student->getPaymentHistory();

        // Get attendance summary
        $attendanceSummary = $student->getAttendanceSummary();

        // Get uniform orders
        $uniformOrders = $student->getUniformOrders();

        if (request()->ajax()) {
            return response()->json([
                'student' => $student,
                'stats' => $stats,
                'paymentHistory' => $paymentHistory,
                'attendanceSummary' => $attendanceSummary,
                'uniformOrders' => $uniformOrders
            ]);
        }

        return view('students.show', compact(
            'student',
            'stats',
            'paymentHistory',
            'attendanceSummary',
            'uniformOrders'
        ));
    }

    /**
     * Show the form for editing the specified student.
     */
    public function edit(Student $student): View
    {
        Gate::authorize('update', $student);

        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        return view('students.edit', compact('student', 'branches', 'academies'));
    }

    /**
     * Update the specified student in storage.
     */
    public function update(Request $request, Student $student): RedirectResponse|JsonResponse
    {
        Gate::authorize('update', $student);

        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'academy_id' => 'required|exists:academies,id',
            'full_name' => 'required|string|max:255',
            'email' => [
                'nullable',
                'email',
                Rule::unique('students', 'email')->ignore($student->id)
            ],
            'phone' => [
                'required',
                'string',
                'regex:/^\+971[0-9]{9}$/',
                Rule::unique('students', 'phone')->ignore($student->id)
            ],
            'nationality' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:500',
            'birth_date' => 'nullable|date|before:today',
            'join_date' => 'required|date',
            'status' => 'required|in:active,inactive,suspended',
            'notes' => 'nullable|string|max:1000',
        ], [
            'phone.regex' => 'Phone number must be in UAE format: +971XXXXXXXXX',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $student->update($validator->validated());

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Student updated successfully',
                    'student' => $student->load(['branch', 'academy'])
                ]);
            }

            return redirect()->route('students.show', $student)
                ->with('success', 'Student updated successfully');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update student: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to update student: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified student from storage.
     */
    public function destroy(Student $student): RedirectResponse|JsonResponse
    {
        Gate::authorize('delete', $student);

        try {
            // Check if student has any related records
            $hasPayments = $student->payments()->exists();
            $hasUniforms = $student->uniforms()->exists();
            $hasAttendances = $student->attendances()->exists();

            if ($hasPayments || $hasUniforms || $hasAttendances) {
                // Soft delete by setting status to inactive instead of hard delete
                $student->update(['status' => 'inactive']);
                $message = 'Student deactivated successfully (has related records)';
            } else {
                $student->delete();
                $message = 'Student deleted successfully';
            }

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message
                ]);
            }

            return redirect()->route('students.index')
                ->with('success', $message);
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete student: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to delete student: ' . $e->getMessage());
        }
    }

    /**
     * Toggle student status (AJAX).
     */
    public function toggleStatus(Student $student): JsonResponse
    {
        Gate::authorize('update', $student);

        try {
            $student->toggleStatus();

            return response()->json([
                'success' => true,
                'message' => 'Student status updated successfully',
                'status' => $student->status,
                'status_text' => $student->status_text
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle bulk actions on students.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,suspend,delete',
            'student_ids' => 'required|array|min:1',
            'student_ids.*' => 'exists:students,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $studentIds = $request->get('student_ids');
            $action = $request->get('action');
            $count = 0;

            foreach ($studentIds as $studentId) {
                $student = Student::find($studentId);
                if (!$student) continue;

                Gate::authorize('update', $student);

                switch ($action) {
                    case 'activate':
                        $student->update(['status' => 'active']);
                        $count++;
                        break;
                    case 'deactivate':
                        $student->update(['status' => 'inactive']);
                        $count++;
                        break;
                    case 'suspend':
                        $student->update(['status' => 'suspended']);
                        $count++;
                        break;
                    case 'delete':
                        // Check if student has related records
                        if (
                            $student->payments()->exists() ||
                            $student->uniforms()->exists() ||
                            $student->attendances()->exists()
                        ) {
                            $student->update(['status' => 'inactive']);
                        } else {
                            $student->delete();
                        }
                        $count++;
                        break;
                }
            }

            $actionText = match ($action) {
                'activate' => 'activated',
                'deactivate' => 'deactivated',
                'suspend' => 'suspended',
                'delete' => 'deleted/deactivated',
                default => 'processed'
            };

            return response()->json([
                'success' => true,
                'message' => "{$count} students {$actionText} successfully"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk action failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export students to Excel.
     */
    public function exportExcel(Request $request)
    {
        Gate::authorize('export', Student::class);

        try {
            $query = Student::with(['branch', 'academy']);

            // Apply same filters as index method
            if ($request->filled('search')) {
                $query->search($request->get('search'));
            }
            if ($request->filled('branch_id')) {
                $query->byBranch($request->get('branch_id'));
            }
            if ($request->filled('academy_id')) {
                $query->byAcademy($request->get('academy_id'));
            }
            if ($request->filled('status')) {
                $status = $request->get('status');
                if ($status === 'active') $query->active();
                elseif ($status === 'inactive') $query->inactive();
                elseif ($status === 'suspended') $query->suspended();
            }

            $students = $query->get();

            $filename = 'students_' . date('Y-m-d_H-i-s') . '.csv';

            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ];

            $callback = function () use ($students) {
                $file = fopen('php://output', 'w');

                // CSV Headers
                fputcsv($file, [
                    'ID',
                    'Full Name',
                    'Email',
                    'Phone',
                    'Nationality',
                    'Branch',
                    'Academy',
                    'Birth Date',
                    'Age',
                    'Join Date',
                    'Status',
                    'Total Payments (AED)',
                    'Pending Payments (AED)',
                    'Attendance Rate (%)',
                    'Uniform Orders',
                    'Address',
                    'Notes'
                ]);

                // CSV Data
                foreach ($students as $student) {
                    fputcsv($file, [
                        $student->id,
                        $student->full_name,
                        $student->email,
                        $student->formatted_phone,
                        $student->nationality,
                        $student->branch->name ?? '',
                        $student->academy->name ?? '',
                        $student->formatted_birth_date,
                        $student->age,
                        $student->formatted_join_date,
                        $student->status_text,
                        number_format($student->total_payments, 2),
                        number_format($student->pending_payments, 2),
                        $student->attendance_rate,
                        $student->uniform_orders_count,
                        $student->address,
                        $student->notes
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Export failed: ' . $e->getMessage());
        }
    }

    /**
     * Export students to PDF.
     */
    public function exportPdf(Request $request)
    {
        Gate::authorize('export', Student::class);

        try {
            $query = Student::with(['branch', 'academy']);

            // Apply same filters as index method
            if ($request->filled('search')) {
                $query->search($request->get('search'));
            }
            if ($request->filled('branch_id')) {
                $query->byBranch($request->get('branch_id'));
            }
            if ($request->filled('academy_id')) {
                $query->byAcademy($request->get('academy_id'));
            }
            if ($request->filled('status')) {
                $status = $request->get('status');
                if ($status === 'active') $query->active();
                elseif ($status === 'inactive') $query->inactive();
                elseif ($status === 'suspended') $query->suspended();
            }

            $students = $query->get();
            $filters = $request->only(['search', 'branch_id', 'academy_id', 'status']);

            return view('students.export-pdf', compact('students', 'filters'));
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'PDF export failed: ' . $e->getMessage());
        }
    }

    /**
     * API endpoint for AJAX requests.
     */
    public function apiIndex(Request $request): JsonResponse
    {
        try {
            $query = Student::with(['branch', 'academy'])
                ->withCount(['payments', 'uniforms', 'attendances']);

            // Apply filters
            if ($request->filled('search')) {
                $query->search($request->get('search'));
            }
            if ($request->filled('branch_id')) {
                $query->byBranch($request->get('branch_id'));
            }
            if ($request->filled('academy_id')) {
                $query->byAcademy($request->get('academy_id'));
            }
            if ($request->filled('status')) {
                $status = $request->get('status');
                if ($status === 'active') $query->active();
                elseif ($status === 'inactive') $query->inactive();
                elseif ($status === 'suspended') $query->suspended();
            }

            $perPage = $request->get('per_page', 15);
            $students = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'students' => $students
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch students: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get student statistics for dashboard.
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $stats = [
                'total_students' => Student::count(),
                'active_students' => Student::active()->count(),
                'inactive_students' => Student::inactive()->count(),
                'suspended_students' => Student::suspended()->count(),
                'new_students_this_month' => Student::whereMonth('join_date', now()->month)
                    ->whereYear('join_date', now()->year)->count(),
                'total_payments' => Payment::completed()->sum('amount'),
                'pending_payments' => Payment::pending()->sum('amount'),
                'average_age' => Student::whereNotNull('birth_date')
                    ->get()
                    ->avg(function ($student) {
                        return $student->age;
                    }),
                'attendance_rate' => Student::active()
                    ->get()
                    ->avg(function ($student) {
                        return $student->attendance_rate;
                    }),
                'uniform_orders' => Uniform::count(),
                'delivered_uniforms' => Uniform::delivered()->count(),
            ];

            // Monthly registration data for charts
            $monthlyRegistrations = [];
            for ($i = 11; $i >= 0; $i--) {
                $date = now()->subMonths($i);
                $monthlyRegistrations[] = [
                    'month' => $date->format('M Y'),
                    'count' => Student::whereMonth('join_date', $date->month)
                        ->whereYear('join_date', $date->year)
                        ->count()
                ];
            }

            return response()->json([
                'success' => true,
                'stats' => $stats,
                'monthlyRegistrations' => $monthlyRegistrations
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get academies by branch (AJAX helper).
     */
    public function getAcademiesByBranch(Request $request): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $academies = Academy::where('branch_id', $branchId)
                ->active()
                ->orderBy('name')
                ->get(['id', 'name']);

            return response()->json([
                'success' => true,
                'academies' => $academies
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch academies: ' . $e->getMessage()
            ], 500);
        }
    }
}
