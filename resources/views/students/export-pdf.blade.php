<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students Report - UAE English Sports Academy</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e53e3e;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #e53e3e;
            font-size: 24px;
            margin: 0 0 10px 0;
            font-weight: bold;
        }
        
        .header h2 {
            color: #666;
            font-size: 18px;
            margin: 0 0 5px 0;
            font-weight: normal;
        }
        
        .header p {
            color: #888;
            font-size: 12px;
            margin: 5px 0;
        }
        
        .filters {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .filters h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
            font-weight: bold;
        }
        
        .filters p {
            margin: 5px 0;
            font-size: 11px;
            color: #6c757d;
        }
        
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            background-color: #fff5f5;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #fed7d7;
        }
        
        .summary-item {
            text-align: center;
            flex: 1;
        }
        
        .summary-item .number {
            font-size: 18px;
            font-weight: bold;
            color: #e53e3e;
            display: block;
        }
        
        .summary-item .label {
            font-size: 10px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px;
        }
        
        table th {
            background-color: #e53e3e;
            color: white;
            padding: 8px 6px;
            text-align: left;
            font-weight: bold;
            font-size: 9px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }
        
        table td {
            padding: 6px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
        }
        
        table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        table tr:hover {
            background-color: #e8f4fd;
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-suspended {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .amount {
            font-weight: bold;
            color: #28a745;
        }
        
        .amount.pending {
            color: #ffc107;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
            padding-top: 15px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .header {
                margin-bottom: 20px;
                padding-bottom: 15px;
            }
            
            table {
                font-size: 9px;
            }
            
            table th {
                font-size: 8px;
                padding: 6px 4px;
            }
            
            table td {
                padding: 4px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>UAE English Sports Academy</h1>
        <h2>Students Report</h2>
        <p>Generated on {{ date('F j, Y \a\t g:i A') }}</p>
        <p>Total Students: {{ $students->count() }}</p>
    </div>

    <!-- Applied Filters -->
    @if(!empty(array_filter($filters)))
        <div class="filters">
            <h3>Applied Filters:</h3>
            @if(!empty($filters['search']))
                <p><strong>Search:</strong> {{ $filters['search'] }}</p>
            @endif
            @if(!empty($filters['branch_id']))
                <p><strong>Branch:</strong> {{ $students->first()?->branch?->name ?? 'N/A' }}</p>
            @endif
            @if(!empty($filters['academy_id']))
                <p><strong>Academy:</strong> {{ $students->first()?->academy?->name ?? 'N/A' }}</p>
            @endif
            @if(!empty($filters['status']))
                <p><strong>Status:</strong> {{ ucfirst($filters['status']) }}</p>
            @endif
        </div>
    @endif

    <!-- Summary Statistics -->
    <div class="summary">
        <div class="summary-item">
            <span class="number">{{ $students->count() }}</span>
            <span class="label">Total Students</span>
        </div>
        <div class="summary-item">
            <span class="number">{{ $students->where('status', 'active')->count() }}</span>
            <span class="label">Active</span>
        </div>
        <div class="summary-item">
            <span class="number">{{ $students->where('status', 'inactive')->count() }}</span>
            <span class="label">Inactive</span>
        </div>
        <div class="summary-item">
            <span class="number">{{ $students->where('status', 'suspended')->count() }}</span>
            <span class="label">Suspended</span>
        </div>
        <div class="summary-item">
            <span class="number">AED {{ number_format($students->sum('total_payments'), 2) }}</span>
            <span class="label">Total Payments</span>
        </div>
    </div>

    <!-- Students Table -->
    <table>
        <thead>
            <tr>
                <th style="width: 5%;">ID</th>
                <th style="width: 20%;">Student Name</th>
                <th style="width: 15%;">Contact</th>
                <th style="width: 12%;">Nationality</th>
                <th style="width: 15%;">Academy</th>
                <th style="width: 10%;">Join Date</th>
                <th style="width: 8%;">Age</th>
                <th style="width: 8%;">Status</th>
                <th style="width: 7%;">Payments</th>
            </tr>
        </thead>
        <tbody>
            @forelse($students as $student)
                <tr>
                    <td>{{ $student->id }}</td>
                    <td>
                        <strong>{{ $student->full_name }}</strong>
                        @if($student->email)
                            <br><small>{{ $student->email }}</small>
                        @endif
                    </td>
                    <td>
                        {{ $student->formatted_phone }}
                        @if($student->address)
                            <br><small>{{ Str::limit($student->address, 30) }}</small>
                        @endif
                    </td>
                    <td>{{ $student->nationality ?? 'N/A' }}</td>
                    <td>
                        <strong>{{ $student->academy->name ?? 'N/A' }}</strong>
                        <br><small>{{ $student->branch->name ?? 'N/A' }}</small>
                    </td>
                    <td>{{ $student->formatted_join_date }}</td>
                    <td>{{ $student->age ?? 'N/A' }}</td>
                    <td>
                        <span class="status-badge status-{{ $student->status }}">
                            {{ $student->status_text }}
                        </span>
                    </td>
                    <td>
                        <span class="amount">AED {{ number_format($student->total_payments, 0) }}</span>
                        @if($student->pending_payments > 0)
                            <br><span class="amount pending">+{{ number_format($student->pending_payments, 0) }} pending</span>
                        @endif
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="9" style="text-align: center; padding: 20px; color: #6c757d;">
                        No students found matching the selected criteria.
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <!-- Additional Statistics -->
    @if($students->count() > 0)
        <div style="margin-top: 30px; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
            <h3 style="margin: 0 0 15px 0; color: #495057; font-size: 14px;">Additional Statistics</h3>
            <div style="display: flex; justify-content: space-between; flex-wrap: wrap;">
                <div style="flex: 1; margin-right: 20px;">
                    <p style="margin: 5px 0; font-size: 11px;"><strong>Average Age:</strong> 
                        {{ number_format($students->whereNotNull('birth_date')->avg(function($student) { return $student->age; }), 1) }} years
                    </p>
                    <p style="margin: 5px 0; font-size: 11px;"><strong>Average Attendance Rate:</strong> 
                        {{ number_format($students->avg('attendance_rate'), 1) }}%
                    </p>
                </div>
                <div style="flex: 1;">
                    <p style="margin: 5px 0; font-size: 11px;"><strong>Total Uniform Orders:</strong> 
                        {{ $students->sum('uniform_orders_count') }}
                    </p>
                    <p style="margin: 5px 0; font-size: 11px;"><strong>Average Payment per Student:</strong> 
                        AED {{ number_format($students->avg('total_payments'), 2) }}
                    </p>
                </div>
            </div>
        </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>&copy; {{ date('Y') }} UAE English Sports Academy. All rights reserved.</p>
        <p>This report contains confidential information and is intended for authorized personnel only.</p>
        <p>Report generated by {{ auth()->user()->name }} ({{ auth()->user()->role }})</p>
    </div>
</body>
</html>
