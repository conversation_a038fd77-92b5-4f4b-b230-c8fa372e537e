@extends('layouts.dashboard')

@section('title', 'Student Management - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Student Management</h1>
                <p class="text-lg text-dark-gray">Manage student profiles and information</p>
                <span class="badge-bank badge-success">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ $students->total() }} Total Students
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('create', App\Models\Student::class)
                <a href="{{ route('students.create') }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                        </path>
                    </svg>
                    Add New Student
                </a>
            @endcan
            @can('export', App\Models\Student::class)
                <div class="flex items-center space-x-2">
                    <button onclick="exportData('excel')" class="btn-bank btn-bank-outline">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        Export Excel
                    </button>
                    <button onclick="exportData('pdf')" class="btn-bank btn-bank-outline">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                            </path>
                        </svg>
                        Export PDF
                    </button>
                </div>
            @endcan
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="studentManagement()" x-init="init()">
        <!-- Advanced Search & Filters -->
        @include('students._filters')

        <!-- Statistics Cards -->
        @include('students._stats')

        <!-- Main Content Card -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div>
                    <h3 class="bank-card-title">All Students</h3>
                    <p class="bank-card-subtitle">
                        Showing {{ $students->firstItem() ?? 0 }} to {{ $students->lastItem() ?? 0 }}
                        of {{ $students->total() }} students
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Bulk Actions -->
                    @can('bulkAction', App\Models\Student::class)
                        <div x-show="selectedStudents.length > 0" x-transition class="flex items-center space-x-2">
                            <span class="text-sm text-dark-gray" x-text="`${selectedStudents.length} selected`"></span>
                            <select x-model="bulkAction" class="form-select-bank text-sm">
                                <option value="">Bulk Actions</option>
                                <option value="activate">Activate Selected</option>
                                <option value="deactivate">Deactivate Selected</option>
                                <option value="suspend">Suspend Selected</option>
                                <option value="delete">Delete Selected</option>
                            </select>
                            <button @click="executeBulkAction()" :disabled="!bulkAction" class="btn-bank btn-bank-sm">
                                Apply
                            </button>
                        </div>
                    @endcan

                    <!-- View Toggle -->
                    <div class="flex items-center bg-off-white rounded-lg p-1">
                        <button @click="viewMode = 'table'" :class="viewMode === 'table' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 10h18M3 6h18m-9 10h9"></path>
                            </svg>
                        </button>
                        <button @click="viewMode = 'grid'" :class="viewMode === 'grid' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                                </path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bank-card-body p-0">
                <!-- Table View -->
                <div x-show="viewMode === 'table'" x-transition>
                    @include('students._table')
                </div>

                <!-- Grid View -->
                <div x-show="viewMode === 'grid'" x-transition>
                    @include('students._grid')
                </div>

                <!-- Pagination -->
                @if ($students->hasPages())
                    <div class="px-6 py-4 border-t border-light-gray">
                        {{ $students->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function studentManagement() {
            return {
                selectedStudents: [],
                bulkAction: '',
                viewMode: localStorage.getItem('studentViewMode') || 'table',

                init() {
                    this.$watch('viewMode', (value) => {
                        localStorage.setItem('studentViewMode', value);
                    });
                },

                toggleStudentSelection(studentId) {
                    const index = this.selectedStudents.indexOf(studentId);
                    if (index > -1) {
                        this.selectedStudents.splice(index, 1);
                    } else {
                        this.selectedStudents.push(studentId);
                    }
                },

                selectAllStudents() {
                    const checkboxes = document.querySelectorAll('input[name="student_ids[]"]');
                    const allSelected = this.selectedStudents.length === checkboxes.length;

                    if (allSelected) {
                        this.selectedStudents = [];
                    } else {
                        this.selectedStudents = Array.from(checkboxes).map(cb => parseInt(cb.value));
                    }
                },

                async executeBulkAction() {
                    if (!this.bulkAction || this.selectedStudents.length === 0) return;

                    const confirmed = await this.confirmBulkAction();
                    if (!confirmed) return;

                    try {
                        const response = await fetch('{{ route('students.bulk-action') }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({
                                action: this.bulkAction,
                                student_ids: this.selectedStudents
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', result.message);
                            window.location.reload();
                        } else {
                            showNotification('error', result.message);
                        }
                    } catch (error) {
                        showNotification('error', 'An error occurred while processing the request.');
                    }
                },

                confirmBulkAction() {
                    const actionText = this.bulkAction.charAt(0).toUpperCase() + this.bulkAction.slice(1);
                    return confirm(
                        `Are you sure you want to ${actionText.toLowerCase()} ${this.selectedStudents.length} selected student(s)?`
                    );
                }
            }
        }

        function exportData(format) {
            const url = format === 'excel' ? '{{ route('students.export.excel') }}' :
                '{{ route('students.export.pdf') }}';
            const params = new URLSearchParams(window.location.search);
            window.open(`${url}?${params.toString()}`, '_blank');
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            alert(message);
        }
    </script>
@endpush
