<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Total Students -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Total Students</p>
                    <p class="text-3xl font-bold text-charcoal-black">{{ number_format($stats['total_students']) }}</p>
                    <p class="text-sm text-success-green">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        All registered students
                    </p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Students -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Active Students</p>
                    <p class="text-3xl font-bold text-success-green">{{ number_format($stats['active_students']) }}</p>
                    <p class="text-sm text-success-green">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Currently enrolled
                    </p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-success-green to-green-600 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Payments -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Total Payments</p>
                    <p class="text-3xl font-bold text-leaders-red">AED {{ number_format($stats['total_payments'], 2) }}</p>
                    <p class="text-sm text-success-green">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        Completed payments
                    </p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Payments -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Pending Payments</p>
                    <p class="text-3xl font-bold text-warning-orange">AED {{ number_format($stats['pending_payments'], 2) }}</p>
                    <p class="text-sm text-warning-orange">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        Awaiting payment
                    </p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-warning-orange to-orange-600 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Statistics Row -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Inactive Students -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Inactive Students</p>
                    <p class="text-2xl font-bold text-gray-500">{{ number_format($stats['inactive_students']) }}</p>
                    <p class="text-sm text-gray-500">Not currently enrolled</p>
                </div>
                <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Suspended Students -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Suspended Students</p>
                    <p class="text-2xl font-bold text-warning-orange">{{ number_format($stats['suspended_students']) }}</p>
                    <p class="text-sm text-warning-orange">Temporarily suspended</p>
                </div>
                <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-warning-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Uniforms -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Uniform Orders</p>
                    <p class="text-2xl font-bold text-purple-600">{{ number_format($stats['total_uniforms']) }}</p>
                    <p class="text-sm text-purple-600">Total orders placed</p>
                </div>
                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Delivered Uniforms -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Delivered Uniforms</p>
                    <p class="text-2xl font-bold text-success-green">{{ number_format($stats['delivered_uniforms']) }}</p>
                    <p class="text-sm text-success-green">Successfully delivered</p>
                </div>
                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-success-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>
