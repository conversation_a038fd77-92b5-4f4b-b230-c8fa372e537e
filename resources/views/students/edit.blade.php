@extends('layouts.dashboard')

@section('title', 'Edit Student - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Edit Student</h1>
                <p class="text-lg text-dark-gray">Update {{ $student->full_name }}'s information</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('students.show', $student) }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View Student
            </a>
            <a href="{{ route('students.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Students
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto" x-data="studentForm()">
        <form method="POST" action="{{ route('students.update', $student) }}" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Basic Information</h3>
                    <p class="bank-card-subtitle">Update the student's personal details</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Full Name -->
                        <div class="md:col-span-2">
                            <label for="full_name" class="form-label-bank required">Full Name</label>
                            <input type="text" id="full_name" name="full_name" 
                                value="{{ old('full_name', $student->full_name) }}"
                                class="form-input-bank @error('full_name') border-red-500 @enderror"
                                placeholder="Enter student's full name" required>
                            @error('full_name')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="form-label-bank">Email Address</label>
                            <input type="email" id="email" name="email" 
                                value="{{ old('email', $student->email) }}"
                                class="form-input-bank @error('email') border-red-500 @enderror"
                                placeholder="<EMAIL>">
                            @error('email')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Phone -->
                        <div>
                            <label for="phone" class="form-label-bank required">Phone Number</label>
                            <input type="tel" id="phone" name="phone" 
                                value="{{ old('phone', $student->phone) }}"
                                class="form-input-bank @error('phone') border-red-500 @enderror"
                                placeholder="+971XXXXXXXXX" pattern="^\+971[0-9]{9}$" required>
                            <p class="form-help-bank">UAE format: +971XXXXXXXXX</p>
                            @error('phone')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Nationality -->
                        <div>
                            <label for="nationality" class="form-label-bank">Nationality</label>
                            <input type="text" id="nationality" name="nationality" 
                                value="{{ old('nationality', $student->nationality) }}"
                                class="form-input-bank @error('nationality') border-red-500 @enderror"
                                placeholder="e.g., UAE, India, Pakistan">
                            @error('nationality')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Birth Date -->
                        <div>
                            <label for="birth_date" class="form-label-bank">Birth Date</label>
                            <input type="date" id="birth_date" name="birth_date" 
                                value="{{ old('birth_date', $student->birth_date?->format('Y-m-d')) }}"
                                class="form-input-bank @error('birth_date') border-red-500 @enderror"
                                max="{{ date('Y-m-d') }}">
                            @error('birth_date')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Address -->
                        <div class="md:col-span-2">
                            <label for="address" class="form-label-bank">Address</label>
                            <textarea id="address" name="address" rows="3"
                                class="form-textarea-bank @error('address') border-red-500 @enderror"
                                placeholder="Enter student's address">{{ old('address', $student->address) }}</textarea>
                            @error('address')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Academy Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Academy Information</h3>
                    <p class="bank-card-subtitle">Update the branch and academy enrollment</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Branch -->
                        <div>
                            <label for="branch_id" class="form-label-bank required">Branch</label>
                            <select id="branch_id" name="branch_id" 
                                class="form-select-bank @error('branch_id') border-red-500 @enderror"
                                @change="onBranchChange($event)" required>
                                <option value="">Select Branch</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}" 
                                        {{ old('branch_id', $student->branch_id) == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('branch_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Academy -->
                        <div>
                            <label for="academy_id" class="form-label-bank required">Academy</label>
                            <select id="academy_id" name="academy_id" 
                                class="form-select-bank @error('academy_id') border-red-500 @enderror" required>
                                <option value="">Select Academy</option>
                                @foreach($academies as $academy)
                                    <option value="{{ $academy->id }}" 
                                        data-branch="{{ $academy->branch_id }}"
                                        {{ old('academy_id', $student->academy_id) == $academy->id ? 'selected' : '' }}>
                                        {{ $academy->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('academy_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Join Date -->
                        <div>
                            <label for="join_date" class="form-label-bank required">Join Date</label>
                            <input type="date" id="join_date" name="join_date" 
                                value="{{ old('join_date', $student->join_date?->format('Y-m-d')) }}"
                                class="form-input-bank @error('join_date') border-red-500 @enderror" required>
                            @error('join_date')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="form-label-bank required">Status</label>
                            <select id="status" name="status" 
                                class="form-select-bank @error('status') border-red-500 @enderror" required>
                                <option value="active" {{ old('status', $student->status) === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status', $student->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                <option value="suspended" {{ old('status', $student->status) === 'suspended' ? 'selected' : '' }}>Suspended</option>
                            </select>
                            @error('status')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Notes -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Additional Information</h3>
                    <p class="bank-card-subtitle">Optional notes and comments</p>
                </div>
                <div class="bank-card-body">
                    <div>
                        <label for="notes" class="form-label-bank">Notes</label>
                        <textarea id="notes" name="notes" rows="4"
                            class="form-textarea-bank @error('notes') border-red-500 @enderror"
                            placeholder="Any additional notes about the student...">{{ old('notes', $student->notes) }}</textarea>
                        @error('notes')
                            <p class="form-error-bank">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Student Statistics (Read-only) -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Student Statistics</h3>
                    <p class="bank-card-subtitle">Current enrollment information</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ $student->days_since_joined }}</div>
                            <div class="text-sm text-dark-gray">Days Enrolled</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-success-green">AED {{ number_format($student->total_payments, 2) }}</div>
                            <div class="text-sm text-dark-gray">Total Payments</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">{{ $student->attendance_rate }}%</div>
                            <div class="text-sm text-dark-gray">Attendance Rate</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-600">{{ $student->uniform_orders_count }}</div>
                            <div class="text-sm text-dark-gray">Uniform Orders</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('students.show', $student) }}" class="btn-bank btn-bank-outline">
                    Cancel
                </a>
                <button type="submit" class="btn-bank">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Student
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
<script>
    function studentForm() {
        return {
            onBranchChange(event) {
                const branchId = event.target.value;
                const academySelect = document.getElementById('academy_id');
                const academyOptions = academySelect.querySelectorAll('option[data-branch]');
                
                // Show/hide academy options based on selected branch
                academyOptions.forEach(option => {
                    if (!branchId || option.dataset.branch === branchId) {
                        option.style.display = 'block';
                    } else {
                        option.style.display = 'none';
                    }
                });
                
                // Reset academy selection if current selection is not valid for new branch
                if (branchId && academySelect.value) {
                    const selectedOption = academySelect.querySelector(`option[value="${academySelect.value}"]`);
                    if (selectedOption && selectedOption.dataset.branch !== branchId) {
                        academySelect.value = '';
                    }
                }
            }
        }
    }

    // Initialize form on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Trigger branch change to show correct academies
        const branchSelect = document.getElementById('branch_id');
        if (branchSelect.value) {
            branchSelect.dispatchEvent(new Event('change'));
        }

        // Phone number formatting
        const phoneInput = document.getElementById('phone');
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.startsWith('971')) {
                value = '+' + value;
            } else if (value.startsWith('0')) {
                value = '+971' + value.substring(1);
            } else if (!value.startsWith('+971') && value.length > 0) {
                value = '+971' + value;
            }
            e.target.value = value;
        });
    });
</script>
@endpush
