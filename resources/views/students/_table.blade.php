<!-- Table View -->
<div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-light-gray">
        <thead class="bg-off-white">
            <tr>
                @can('bulkAction', App\Models\Student::class)
                    <th class="px-6 py-3 text-left">
                        <input type="checkbox" @change="selectAllStudents()" 
                            :checked="selectedStudents.length > 0 && selectedStudents.length === document.querySelectorAll('input[name=\'student_ids[]\']').length"
                            class="form-checkbox-bank">
                    </th>
                @endcan
                <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Student Info
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Contact & Location
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Academy Details
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Financial Summary
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Status & Activity
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-light-gray">
            @forelse($students as $student)
                <tr class="hover:bg-off-white transition-colors duration-200">
                    @can('bulkAction', App\Models\Student::class)
                        <td class="px-6 py-4">
                            <input type="checkbox" name="student_ids[]" value="{{ $student->id }}"
                                @change="toggleStudentSelection({{ $student->id }})"
                                :checked="selectedStudents.includes({{ $student->id }})"
                                class="form-checkbox-bank">
                        </td>
                    @endcan
                    
                    <!-- Student Info -->
                    <td class="px-6 py-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                {{ substr($student->full_name, 0, 1) }}
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-charcoal-black">
                                    <a href="{{ route('students.show', $student) }}" class="hover:text-leaders-red transition-colors">
                                        {{ $student->full_name }}
                                    </a>
                                </div>
                                <div class="text-sm text-dark-gray">
                                    ID: {{ $student->id }}
                                    @if($student->age)
                                        • Age: {{ $student->age }}
                                    @endif
                                </div>
                                @if($student->birth_date)
                                    <div class="text-xs text-gray-500">
                                        Born: {{ $student->formatted_birth_date }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </td>

                    <!-- Contact & Location -->
                    <td class="px-6 py-4">
                        <div class="text-sm">
                            @if($student->email)
                                <div class="flex items-center text-dark-gray mb-1">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    {{ $student->email }}
                                </div>
                            @endif
                            <div class="flex items-center text-dark-gray mb-1">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                {{ $student->formatted_phone }}
                            </div>
                            @if($student->nationality)
                                <div class="flex items-center text-dark-gray">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ $student->nationality }}
                                </div>
                            @endif
                        </div>
                    </td>

                    <!-- Academy Details -->
                    <td class="px-6 py-4">
                        <div class="text-sm">
                            <div class="font-medium text-charcoal-black mb-1">
                                {{ $student->academy->name ?? 'N/A' }}
                            </div>
                            <div class="text-dark-gray mb-1">
                                Branch: {{ $student->branch->name ?? 'N/A' }}
                            </div>
                            <div class="text-xs text-gray-500">
                                Joined: {{ $student->formatted_join_date }}
                            </div>
                            <div class="text-xs text-gray-500">
                                {{ $student->days_since_joined }} days enrolled
                            </div>
                        </div>
                    </td>

                    <!-- Financial Summary -->
                    <td class="px-6 py-4">
                        <div class="text-sm">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-dark-gray">Total:</span>
                                <span class="font-medium text-success-green">
                                    AED {{ number_format($student->total_payments, 2) }}
                                </span>
                            </div>
                            @if($student->pending_payments > 0)
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-dark-gray">Pending:</span>
                                    <span class="font-medium text-warning-orange">
                                        AED {{ number_format($student->pending_payments, 2) }}
                                    </span>
                                </div>
                            @endif
                            <div class="text-xs text-gray-500">
                                {{ $student->payments_count }} payment(s)
                            </div>
                        </div>
                    </td>

                    <!-- Status & Activity -->
                    <td class="px-6 py-4">
                        <div class="space-y-2">
                            <span class="badge-bank {{ $student->status_badge_class }}">
                                {{ $student->status_text }}
                            </span>
                            @if($student->attendance_rate > 0)
                                <div class="text-xs text-gray-500">
                                    Attendance: {{ $student->attendance_rate }}%
                                </div>
                            @endif
                            @if($student->uniform_orders_count > 0)
                                <div class="text-xs text-gray-500">
                                    {{ $student->uniform_orders_count }} uniform order(s)
                                </div>
                            @endif
                        </div>
                    </td>

                    <!-- Actions -->
                    <td class="px-6 py-4 text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            @can('view', $student)
                                <a href="{{ route('students.show', $student) }}" 
                                   class="text-blue-600 hover:text-blue-900 transition-colors"
                                   title="View Details">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </a>
                            @endcan
                            
                            @can('update', $student)
                                <a href="{{ route('students.edit', $student) }}" 
                                   class="text-indigo-600 hover:text-indigo-900 transition-colors"
                                   title="Edit Student">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </a>
                                
                                <button @click="toggleStatus({{ $student->id }})" 
                                        class="text-yellow-600 hover:text-yellow-900 transition-colors"
                                        title="Toggle Status">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                    </svg>
                                </button>
                            @endcan
                            
                            @can('delete', $student)
                                <button @click="deleteStudent({{ $student->id }})" 
                                        class="text-red-600 hover:text-red-900 transition-colors"
                                        title="Delete Student">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            @endcan
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center justify-center">
                            <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                                </path>
                            </svg>
                            <h3 class="text-lg font-medium text-dark-gray mb-2">No students found</h3>
                            <p class="text-gray-500 mb-4">No students match your current filters.</p>
                            @can('create', App\Models\Student::class)
                                <a href="{{ route('students.create') }}" class="btn-bank">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Add First Student
                                </a>
                            @endcan
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

@push('scripts')
<script>
    async function toggleStatus(studentId) {
        try {
            const response = await fetch(`/students/${studentId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                showNotification('success', result.message);
                window.location.reload();
            } else {
                showNotification('error', result.message);
            }
        } catch (error) {
            showNotification('error', 'An error occurred while updating status.');
        }
    }

    async function deleteStudent(studentId) {
        if (!confirm('Are you sure you want to delete this student? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch(`/students/${studentId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                showNotification('success', result.message);
                window.location.reload();
            } else {
                showNotification('error', result.message);
            }
        } catch (error) {
            showNotification('error', 'An error occurred while deleting the student.');
        }
    }
</script>
@endpush
