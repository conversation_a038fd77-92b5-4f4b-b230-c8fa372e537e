@extends('layouts.dashboard')

@section('title', $student->full_name . ' - Student Profile')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center text-white font-bold text-xl">
                {{ substr($student->full_name, 0, 1) }}
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ $student->full_name }}</h1>
                <p class="text-lg text-dark-gray">Student Profile • ID: {{ $student->id }}</p>
                <span class="badge-bank {{ $student->status_badge_class }}">
                    {{ $student->status_text }}
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('update', $student)
                <a href="{{ route('students.edit', $student) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Student
                </a>
            @endcan
            <a href="{{ route('students.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Students
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Student Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total Payments -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-dark-gray">Total Payments</p>
                            <p class="text-2xl font-bold text-success-green">AED {{ number_format($student->total_payments, 2) }}</p>
                        </div>
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-success-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Payments -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-dark-gray">Pending Payments</p>
                            <p class="text-2xl font-bold text-warning-orange">AED {{ number_format($student->pending_payments, 2) }}</p>
                        </div>
                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-warning-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendance Rate -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-dark-gray">Attendance Rate</p>
                            <p class="text-2xl font-bold text-blue-600">{{ $student->attendance_rate }}%</p>
                        </div>
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Uniform Orders -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-dark-gray">Uniform Orders</p>
                            <p class="text-2xl font-bold text-purple-600">{{ $student->uniform_orders_count }}</p>
                        </div>
                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Student Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Personal Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Personal Information</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="form-label-bank">Full Name</label>
                                <p class="text-charcoal-black font-medium">{{ $student->full_name }}</p>
                            </div>
                            @if($student->email)
                                <div>
                                    <label class="form-label-bank">Email</label>
                                    <p class="text-charcoal-black">{{ $student->email }}</p>
                                </div>
                            @endif
                            <div>
                                <label class="form-label-bank">Phone</label>
                                <p class="text-charcoal-black">{{ $student->formatted_phone }}</p>
                            </div>
                            @if($student->nationality)
                                <div>
                                    <label class="form-label-bank">Nationality</label>
                                    <p class="text-charcoal-black">{{ $student->nationality }}</p>
                                </div>
                            @endif
                            @if($student->birth_date)
                                <div>
                                    <label class="form-label-bank">Birth Date</label>
                                    <p class="text-charcoal-black">{{ $student->formatted_birth_date }} ({{ $student->age }} years old)</p>
                                </div>
                            @endif
                            <div>
                                <label class="form-label-bank">Join Date</label>
                                <p class="text-charcoal-black">{{ $student->formatted_join_date }} ({{ $student->days_since_joined }} days ago)</p>
                            </div>
                            @if($student->address)
                                <div class="md:col-span-2">
                                    <label class="form-label-bank">Address</label>
                                    <p class="text-charcoal-black">{{ $student->address }}</p>
                                </div>
                            @endif
                            @if($student->notes)
                                <div class="md:col-span-2">
                                    <label class="form-label-bank">Notes</label>
                                    <p class="text-charcoal-black">{{ $student->notes }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Payment History -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Payment History</h3>
                        <span class="badge-bank badge-info">{{ count($paymentHistory) }} payments</span>
                    </div>
                    <div class="bank-card-body p-0">
                        @if(count($paymentHistory) > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-light-gray">
                                    <thead class="bg-off-white">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase">Date</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase">Amount</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase">Method</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-light-gray">
                                        @foreach($paymentHistory as $payment)
                                            <tr>
                                                <td class="px-6 py-4 text-sm text-charcoal-black">{{ $payment['date'] }}</td>
                                                <td class="px-6 py-4 text-sm font-medium text-success-green">AED {{ number_format($payment['amount'], 2) }}</td>
                                                <td class="px-6 py-4">
                                                    <span class="badge-bank badge-{{ $payment['status'] === 'completed' ? 'success' : 'warning' }}">
                                                        {{ ucfirst($payment['status']) }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-dark-gray">{{ $payment['method'] }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                <p class="text-dark-gray">No payment history available</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar Information -->
            <div class="space-y-6">
                <!-- Academy Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Academy Information</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="space-y-4">
                            <div>
                                <label class="form-label-bank">Branch</label>
                                <p class="text-charcoal-black font-medium">{{ $student->branch->name ?? 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="form-label-bank">Academy</label>
                                <p class="text-charcoal-black font-medium">{{ $student->academy->name ?? 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="form-label-bank">Status</label>
                                <span class="badge-bank {{ $student->status_badge_class }}">
                                    {{ $student->status_text }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attendance Summary -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Attendance Summary</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-dark-gray">Total Sessions:</span>
                                <span class="font-medium">{{ $attendanceSummary['total_sessions'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-dark-gray">Present:</span>
                                <span class="font-medium text-success-green">{{ $attendanceSummary['present'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-dark-gray">Absent:</span>
                                <span class="font-medium text-red-600">{{ $attendanceSummary['absent'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-dark-gray">Late:</span>
                                <span class="font-medium text-warning-orange">{{ $attendanceSummary['late'] }}</span>
                            </div>
                            <div class="border-t pt-4">
                                <div class="flex justify-between">
                                    <span class="text-dark-gray font-medium">Attendance Rate:</span>
                                    <span class="font-bold text-blue-600">{{ $attendanceSummary['attendance_rate'] }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Uniform Orders -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Uniform Orders</h3>
                        <span class="badge-bank badge-info">{{ count($uniformOrders) }} orders</span>
                    </div>
                    <div class="bank-card-body">
                        @if(count($uniformOrders) > 0)
                            <div class="space-y-3">
                                @foreach($uniformOrders as $uniform)
                                    <div class="border border-light-gray rounded-lg p-3">
                                        <div class="flex justify-between items-start mb-2">
                                            <span class="font-medium text-charcoal-black">{{ $uniform['item'] }}</span>
                                            <span class="badge-bank badge-{{ $uniform['status'] === 'delivered' ? 'success' : 'warning' }}">
                                                {{ ucfirst($uniform['status']) }}
                                            </span>
                                        </div>
                                        <div class="text-sm text-dark-gray">
                                            <p>Size: {{ $uniform['size'] }} • Qty: {{ $uniform['quantity'] }}</p>
                                            <p>Amount: AED {{ number_format($uniform['amount'], 2) }}</p>
                                            <p>Ordered: {{ $uniform['order_date'] }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-6">
                                <svg class="w-10 h-10 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                                <p class="text-dark-gray">No uniform orders</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
