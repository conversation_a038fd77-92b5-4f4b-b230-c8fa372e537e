<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Programs Export - UAE English Sports Academy</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 10px;
            line-height: 1.4;
            color: #333;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #E53E3E;
        }

        .header h1 {
            color: #E53E3E;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .header h2 {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .header .export-info {
            color: #888;
            font-size: 9px;
        }

        .summary {
            background-color: #f8f9fa;
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }

        .summary h3 {
            color: #E53E3E;
            font-size: 12px;
            margin-bottom: 8px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }

        .summary-item {
            text-align: center;
        }

        .summary-item .value {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }

        .summary-item .label {
            font-size: 8px;
            color: #666;
            text-transform: uppercase;
        }

        .table-container {
            width: 100%;
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 8px;
        }

        th {
            background-color: #E53E3E;
            color: white;
            padding: 6px 4px;
            text-align: left;
            font-weight: bold;
            font-size: 8px;
            border: 1px solid #ddd;
        }

        td {
            padding: 4px;
            border: 1px solid #ddd;
            vertical-align: top;
            word-wrap: break-word;
        }

        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tr:hover {
            background-color: #e9ecef;
        }

        .status-active {
            color: #28a745;
            font-weight: bold;
        }

        .status-inactive {
            color: #6c757d;
            font-weight: bold;
        }

        .price {
            text-align: right;
            font-weight: bold;
            color: #E53E3E;
        }

        .center {
            text-align: center;
        }

        .days-list {
            font-size: 7px;
        }

        .description {
            max-width: 150px;
            word-wrap: break-word;
            font-size: 7px;
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 8px;
            color: #666;
            padding: 10px;
            border-top: 1px solid #ddd;
            background-color: white;
        }

        .page-break {
            page-break-before: always;
        }

        @media print {
            .footer {
                position: fixed;
                bottom: 0;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="header">
        <h1>UAE English Sports Academy</h1>
        <h2>Programs Export Report</h2>
        <div class="export-info">
            Generated on {{ now()->format('F d, Y \a\t g:i A') }} | Total Programs: {{ $programs->count() }}
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="summary">
        <h3>Summary Statistics</h3>
        <div class="summary-grid">
            <div class="summary-item">
                <div class="value">{{ $programs->count() }}</div>
                <div class="label">Total Programs</div>
            </div>
            <div class="summary-item">
                <div class="value">{{ $programs->where('status', true)->count() }}</div>
                <div class="label">Active Programs</div>
            </div>
            <div class="summary-item">
                <div class="value">{{ number_format($programs->where('status', true)->avg('price') ?? 0, 2) }} AED
                </div>
                <div class="label">Average Price</div>
            </div>
            <div class="summary-item">
                <div class="value">{{ number_format($programs->where('status', true)->sum('price') ?? 0, 2) }} AED
                </div>
                <div class="label">Total Revenue</div>
            </div>
        </div>
    </div>

    <!-- Programs Table -->
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th style="width: 5%;">ID</th>
                    <th style="width: 15%;">Program Name</th>
                    <th style="width: 12%;">Academy</th>
                    <th style="width: 12%;">Branch</th>
                    <th style="width: 10%;">Days</th>
                    <th style="width: 8%;">Time</th>
                    <th style="width: 6%;">Classes</th>
                    <th style="width: 8%;">Price</th>
                    <th style="width: 8%;">Students</th>
                    <th style="width: 6%;">Status</th>
                    <th style="width: 10%;">Description</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($programs as $program)
                    <tr>
                        <td class="center">{{ $program->id }}</td>
                        <td><strong>{{ $program->name }}</strong></td>
                        <td>{{ $program->academy->name ?? 'N/A' }}</td>
                        <td>{{ $program->academy->branch->name ?? 'N/A' }}</td>
                        <td class="days-list">
                            @php $days = $program->getDaysArray(); @endphp
                            @if (!empty($days))
                                {{ implode(', ', $days) }}
                            @else
                                N/A
                            @endif
                        </td>
                        <td class="center">
                            @if ($program->start_time && $program->end_time)
                                {{ \Carbon\Carbon::parse($program->start_time)->format('H:i') }}-{{ \Carbon\Carbon::parse($program->end_time)->format('H:i') }}
                            @else
                                N/A
                            @endif
                        </td>
                        <td class="center">{{ $program->classes }}</td>
                        <td class="price">{{ number_format($program->price, 2) }}</td>
                        <td class="center">
                            {{ $program->student_count }}
                            @if ($program->max_students)
                                /{{ $program->max_students }}
                            @endif
                        </td>
                        <td class="center">
                            <span class="{{ $program->status ? 'status-active' : 'status-inactive' }}">
                                {{ $program->status ? 'Active' : 'Inactive' }}
                            </span>
                        </td>
                        <td class="description">
                            {{ $program->description ? Str::limit($program->description, 80) : 'N/A' }}
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Programs by Academy -->
    @if ($programs->groupBy('academy.name')->count() > 1)
        <div class="page-break"></div>
        <div class="summary">
            <h3>Programs by Academy</h3>
            <table style="margin-top: 10px;">
                <thead>
                    <tr>
                        <th style="width: 40%;">Academy Name</th>
                        <th style="width: 25%;">Branch</th>
                        <th style="width: 15%;">Total Programs</th>
                        <th style="width: 20%;">Average Price (AED)</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($programs->groupBy('academy.name') as $academyName => $academyPrograms)
                        <tr>
                            <td><strong>{{ $academyName }}</strong></td>
                            <td>{{ $academyPrograms->first()->academy->branch->name ?? 'N/A' }}</td>
                            <td class="center">{{ $academyPrograms->count() }}</td>
                            <td class="price">{{ number_format($academyPrograms->avg('price'), 2) }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <div>UAE English Sports Academy - Programs Export Report</div>
        <div>Page <span class="pagenum"></span> | Generated on {{ now()->format('Y-m-d H:i:s') }}</div>
    </div>

    <script type="text/php">
        if (isset($pdf)) {
            $pdf->page_script('
                $font = $fontMetrics->get_font("Arial, Helvetica, sans-serif", "normal");
                $pdf->text(520, 820, "Page " . $PAGE_NUM . " of " . $PAGE_COUNT, $font, 8);
            ');
        }
    </script>
</body>

</html>
