<!-- Programs Table -->
<div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-light-gray">
        <thead class="bg-off-white">
            <tr>
                @can('bulkAction', App\Models\Program::class)
                    <th scope="col" class="px-6 py-3 text-left">
                        <input type="checkbox" @change="selectAllPrograms()"
                            :checked="selectedPrograms.length > 0 && selectedPrograms.length === document.querySelectorAll(
                                'input[name=\'program_ids[]\']').length"
                            class="form-checkbox text-leaders-red border-medium-gray rounded focus:ring-leaders-red focus:ring-offset-0">
                    </th>
                @endcan
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Program Details
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Academy & Branch
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Schedule
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Classes & Price
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Status
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase tracking-wider">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-light-gray">
            @forelse($programs as $program)
                <tr class="hover:bg-off-white transition-colors duration-200">
                    @can('bulkAction', App\Models\Program::class)
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" name="program_ids[]" value="{{ $program->id }}"
                                @change="toggleProgramSelection({{ $program->id }})"
                                :checked="selectedPrograms.includes({{ $program->id }})"
                                class="form-checkbox text-leaders-red border-medium-gray rounded focus:ring-leaders-red focus:ring-offset-0">
                        </td>
                    @endcan

                    <!-- Program Details -->
                    <td class="px-6 py-4">
                        <div class="flex items-center">
                            <div
                                class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                    </path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-charcoal-black">{{ $program->name }}</div>
                                <div class="text-sm text-dark-gray">ID: #{{ $program->id }}</div>
                                @if ($program->description)
                                    <div class="text-xs text-medium-gray mt-1">
                                        {{ Str::limit($program->description, 50) }}</div>
                                @endif
                            </div>
                        </div>
                    </td>

                    <!-- Academy & Branch -->
                    <td class="px-6 py-4">
                        <div class="text-sm font-medium text-charcoal-black">{{ $program->academy->name }}</div>
                        <div class="text-sm text-dark-gray flex items-center">
                            <svg class="w-4 h-4 mr-1 text-medium-gray" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                </path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z">
                                </path>
                            </svg>
                            {{ $program->academy->branch->name }}
                        </div>
                    </td>

                    <!-- Schedule -->
                    <td class="px-6 py-4">
                        <div class="text-sm text-charcoal-black">
                            <div class="flex flex-wrap gap-1 mb-1">
                                @php $days = $program->getDaysArray(); @endphp
                                @if (!empty($days))
                                    @foreach ($days as $day)
                                        <span
                                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ $day }}
                                        </span>
                                    @endforeach
                                @else
                                    <span class="text-xs text-medium-gray">No schedule set</span>
                                @endif
                            </div>
                            @if ($program->start_time && $program->end_time)
                                <div class="text-xs text-dark-gray">
                                    {{ \Carbon\Carbon::parse($program->start_time)->format('g:i A') }} -
                                    {{ \Carbon\Carbon::parse($program->end_time)->format('g:i A') }}
                                </div>
                            @endif
                        </div>
                    </td>

                    <!-- Classes & Price -->
                    <td class="px-6 py-4">
                        <div class="text-sm font-medium text-charcoal-black">{{ $program->classes }} Classes</div>
                        <div class="text-sm font-bold text-leaders-red">{{ number_format($program->price, 2) }} AED
                        </div>
                        @if ($program->max_students)
                            <div class="text-xs text-dark-gray">Max: {{ $program->max_students }} students</div>
                        @endif
                    </td>

                    <!-- Status -->
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center space-x-2">
                            <span class="badge-bank {{ $program->status ? 'badge-success' : 'badge-neutral' }}">
                                {{ $program->status ? 'Active' : 'Inactive' }}
                            </span>
                            @can('toggleStatus', $program)
                                <button onclick="toggleProgramStatus({{ $program->id }})"
                                    class="text-medium-gray hover:text-leaders-red transition-colors duration-200"
                                    title="Toggle Status">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                    </svg>
                                </button>
                            @endcan
                        </div>
                    </td>

                    <!-- Actions -->
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            @can('view', $program)
                                <a href="{{ route('programs.show', $program) }}"
                                    class="text-medium-gray hover:text-info-blue transition-colors duration-200"
                                    title="View Program">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan

                            @can('update', $program)
                                <a href="{{ route('programs.edit', $program) }}"
                                    class="text-medium-gray hover:text-warning-orange transition-colors duration-200"
                                    title="Edit Program">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan

                            @can('delete', $program)
                                <button onclick="deleteProgram({{ $program->id }})"
                                    class="text-medium-gray hover:text-error-red transition-colors duration-200"
                                    title="Delete Program">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            @endcan
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center justify-center">
                            <svg class="w-16 h-16 text-medium-gray mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                </path>
                            </svg>
                            <h3 class="text-lg font-medium text-dark-gray mb-2">No Programs Found</h3>
                            <p class="text-medium-gray mb-4">There are no programs matching your search criteria.</p>
                            @can('create', App\Models\Program::class)
                                <a href="{{ route('programs.create') }}" class="btn-bank">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                                        </path>
                                    </svg>
                                    Create First Program
                                </a>
                            @endcan
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

@push('scripts')
    <script>
        async function toggleProgramStatus(programId) {
            try {
                const response = await fetch(`/programs/${programId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('success', result.message);
                    window.location.reload();
                } else {
                    showNotification('error', result.message);
                }
            } catch (error) {
                showNotification('error', 'An error occurred while updating the program status.');
            }
        }

        function deleteProgram(programId) {
            if (confirm('Are you sure you want to delete this program? This action cannot be undone.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/programs/${programId}`;
                form.innerHTML = `
                <input type="hidden" name="_method" value="DELETE">
                <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').content}">
            `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
@endpush
