<!-- Programs Grid View -->
<div class="p-6">
    @if ($programs->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            @foreach ($programs as $program)
                <div
                    class="bg-white border border-medium-gray rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                    <!-- Card Header -->
                    <div class="p-6 pb-4">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-4 flex-1">
                                @can('bulkAction', App\Models\Program::class)
                                    <input type="checkbox" name="program_ids[]" value="{{ $program->id }}"
                                        @change="toggleProgramSelection({{ $program->id }})"
                                        :checked="selectedPrograms.includes({{ $program->id }})"
                                        class="form-checkbox text-leaders-red border-medium-gray rounded focus:ring-leaders-red focus:ring-offset-0">
                                @endcan
                                <div
                                    class="w-14 h-14 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-xl flex items-center justify-center shadow-lg">
                                    <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                        </path>
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <h4 class="font-bold text-lg text-charcoal-black mb-1 truncate">{{ $program->name }}
                                    </h4>
                                    <p class="text-sm text-dark-gray flex items-center">
                                        <svg class="w-4 h-4 mr-1.5 text-medium-gray flex-shrink-0" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                            </path>
                                        </svg>
                                        <span class="truncate">{{ $program->academy->name }}</span>
                                    </p>
                                    <p class="text-xs text-medium-gray flex items-center mt-1">
                                        <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                            </path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z">
                                            </path>
                                        </svg>
                                        <span class="truncate">{{ $program->academy->branch->name }}</span>
                                    </p>
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-2">
                                <span
                                    class="badge-bank {{ $program->status ? 'badge-success' : 'badge-neutral' }} px-3 py-1.5 text-sm font-medium">
                                    {{ $program->status ? 'Active' : 'Inactive' }}
                                </span>
                                @can('toggleStatus', $program)
                                    <button onclick="toggleProgramStatus({{ $program->id }})"
                                        class="text-medium-gray hover:text-leaders-red transition-colors duration-200"
                                        title="Toggle Status">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                        </svg>
                                    </button>
                                @endcan
                            </div>
                        </div>

                        <!-- Program Details -->
                        <div class="space-y-3">
                            <!-- Schedule -->
                            <div>
                                <p class="text-xs font-medium text-dark-gray uppercase tracking-wide mb-2">Schedule</p>
                                <div class="flex flex-wrap gap-1 mb-2">
                                    @php $days = $program->getDaysArray(); @endphp
                                    @if (!empty($days))
                                        @foreach ($days as $day)
                                            <span
                                                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ $day }}
                                            </span>
                                        @endforeach
                                    @else
                                        <span class="text-xs text-medium-gray">No schedule set</span>
                                    @endif
                                </div>
                                @if ($program->start_time && $program->end_time)
                                    <p class="text-xs text-medium-gray">
                                        {{ \Carbon\Carbon::parse($program->start_time)->format('g:i A') }} -
                                        {{ \Carbon\Carbon::parse($program->end_time)->format('g:i A') }}
                                    </p>
                                @endif
                            </div>

                            <!-- Statistics Grid -->
                            <div class="grid grid-cols-2 gap-3">
                                <div
                                    class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-3 text-center border border-green-200">
                                    <div class="text-xl font-bold text-green-800 mb-1">{{ $program->classes }}</div>
                                    <div class="text-xs font-medium text-green-700 uppercase tracking-wide">Classes
                                    </div>
                                </div>
                                <div
                                    class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-3 text-center border border-red-200">
                                    <div class="text-xl font-bold text-red-800 mb-1">
                                        {{ number_format($program->price, 0) }}</div>
                                    <div class="text-xs font-medium text-red-700 uppercase tracking-wide">AED</div>
                                </div>
                            </div>

                            @if ($program->max_students)
                                <div
                                    class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-3 border border-purple-200">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-xs font-medium text-purple-700 uppercase tracking-wide">
                                                Capacity</p>
                                            <p class="text-lg font-bold text-purple-900">{{ $program->max_students }}
                                                Students</p>
                                        </div>
                                        <div
                                            class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                                                </path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if ($program->description)
                                <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                                    <p class="text-xs font-medium text-gray-700 uppercase tracking-wide mb-1">
                                        Description</p>
                                    <p class="text-sm text-gray-600">{{ Str::limit($program->description, 100) }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Card Footer -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-100">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                @can('view', $program)
                                    <a href="{{ route('programs.show', $program) }}"
                                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                            </path>
                                        </svg>
                                        View
                                    </a>
                                @endcan
                                @can('update', $program)
                                    <a href="{{ route('programs.edit', $program) }}"
                                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-leaders-red border border-leaders-red rounded-lg hover:bg-leaders-deep-red transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                            </path>
                                        </svg>
                                        Edit
                                    </a>
                                @endcan
                            </div>
                            <div class="flex items-center space-x-2">
                                @can('delete', $program)
                                    <button onclick="deleteProgram({{ $program->id }})"
                                        class="text-medium-gray hover:text-error-red transition-colors duration-200"
                                        title="Delete Program">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                            </path>
                                        </svg>
                                    </button>
                                @endcan
                                <div class="text-xs text-medium-gray font-medium">
                                    {{ $program->created_at->format('M d, Y') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="flex flex-col items-center justify-center">
                <svg class="w-24 h-24 text-medium-gray mb-6" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                    </path>
                </svg>
                <h3 class="text-2xl font-bold text-dark-gray mb-3">No Programs Found</h3>
                <p class="text-medium-gray mb-6 max-w-md">There are no programs matching your search criteria. Try
                    adjusting your filters or create a new program.</p>
                @can('create', App\Models\Program::class)
                    <a href="{{ route('programs.create') }}" class="btn-bank">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                            </path>
                        </svg>
                        Create First Program
                    </a>
                @endcan
            </div>
        </div>
    @endif
</div>
